-- 插入充值记录测试数据
-- 为用户ID 6 插入一些充值记录

INSERT IGNORE INTO user_asset_transactions (
    user_id, transaction_type, amount, balance_before, balance_after,
    related_type, related_id, description, transaction_no, status, created_at, updated_at
) VALUES 
-- 第一笔充值：支付宝充值 100,000 贝壳
(6, 'shells_in', 100000.00, 0.00, 100000.00, 'recharge', NULL, '支付宝充值', 'TXN202501021430001', 'completed', '2025-01-02 14:30:00', '2025-01-02 14:31:15'),

-- 第二笔充值：微信充值 50,000 贝壳
(6, 'shells_in', 50000.00, 100000.00, 150000.00, 'recharge', NULL, '微信支付充值', 'TXN202501011015001', 'completed', '2025-01-01 10:15:00', '2025-01-01 10:16:30'),

-- 第三笔充值：银行卡充值 200,000 贝壳
(6, 'shells_in', 200000.00, 150000.00, 350000.00, 'recharge', NULL, '银行卡充值', 'TXN202412301200001', 'completed', '2024-12-30 12:00:00', '2024-12-30 12:02:45'),

-- 第四笔充值：支付宝充值 30,000 贝壳（处理中）
(6, 'shells_in', 30000.00, 350000.00, 350000.00, 'recharge', NULL, '支付宝充值', 'TXN202501030900001', 'pending', '2025-01-03 09:00:00', '2025-01-03 09:00:00'),

-- 第五笔充值：微信充值 80,000 贝壳（失败）
(6, 'shells_in', 80000.00, 350000.00, 350000.00, 'recharge', NULL, '微信支付充值', 'TXN202501021800001', 'failed', '2025-01-02 18:00:00', '2025-01-02 18:05:00');

-- 更新用户资产表中的余额（确保数据一致性）
UPDATE user_assets 
SET shells_balance = 350000.00, updated_at = NOW() 
WHERE user_id = 6;
