import { requestClient } from '#/api/request';
import type { NewsManagementApi } from './types';

// ==================== 新闻管理 API ====================

/**
 * 获取新闻列表
 */
export async function getNewsList(params: NewsManagementApi.NewsListParams = {}) {
  return requestClient.get<NewsManagementApi.NewsListResponse>('/admin/news', {
    params,
  });
}

/**
 * 获取新闻详情
 */
export async function getNewsDetail(id: number) {
  return requestClient.get<NewsManagementApi.NewsDetailResponse>(`/admin/news/${id}`);
}

/**
 * 创建新闻
 */
export async function createNews(data: NewsManagementApi.CreateNewsParams) {
  return requestClient.post<NewsManagementApi.OperationResponse>('/admin/news', data);
}

/**
 * 更新新闻
 */
export async function updateNews(id: number, data: NewsManagementApi.UpdateNewsParams) {
  return requestClient.put<NewsManagementApi.OperationResponse>(`/admin/news/${id}`, data);
}

/**
 * 删除新闻
 */
export async function deleteNews(id: number) {
  return requestClient.delete<NewsManagementApi.OperationResponse>(`/admin/news/${id}`);
}

/**
 * 发布/取消发布/归档新闻
 */
export async function publishNews(id: number, data: NewsManagementApi.PublishNewsParams) {
  return requestClient.post<NewsManagementApi.OperationResponse>(`/admin/news/${id}/publish`, data);
}

/**
 * 批量操作新闻
 */
export async function batchOperateNews(data: NewsManagementApi.BatchOperationParams) {
  return requestClient.post<NewsManagementApi.OperationResponse>('/admin/news/batch-operation', data);
}

// ==================== 分类管理 API（仅供新闻管理使用） ====================

/**
 * 获取所有分类（用于下拉选择）
 */
export async function getAllCategories() {
  return requestClient.get<{ data: NewsManagementApi.NewsCategory[] }>('/admin/news/categories/all');
}

// ==================== 标签管理 API ====================

/**
 * 获取标签列表
 */
export async function getTagList(params: NewsManagementApi.TagListParams = {}) {
  return requestClient.get<NewsManagementApi.TagListResponse>('/admin/news/tags', {
    params,
  });
}

/**
 * 获取所有标签（用于标签选择）
 */
export async function getAllTags() {
  return requestClient.get<{ data: NewsManagementApi.NewsTag[] }>('/admin/news/tags/all');
}

/**
 * 创建标签
 */
export async function createTag(data: NewsManagementApi.CreateTagParams) {
  return requestClient.post<NewsManagementApi.OperationResponse>('/admin/news/tags', data);
}

/**
 * 删除标签
 */
export async function deleteTag(id: number) {
  return requestClient.delete<NewsManagementApi.OperationResponse>(`/admin/news/tags/${id}`);
}

// ==================== 文件上传 API ====================

/**
 * 上传新闻图片
 */
export async function uploadNewsImage(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return requestClient.post<NewsManagementApi.UploadResponse>('/admin/upload/news-image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// ==================== 统计数据 API ====================

/**
 * 获取新闻统计数据
 */
export async function getNewsStats() {
  return requestClient.get<{ data: NewsManagementApi.NewsStats }>('/admin/news/stats');
}

// ==================== 公开接口（用于前台预览） ====================

/**
 * 获取公开新闻列表（用于前台预览）
 */
export async function getPublicNewsList(params: any = {}) {
  return requestClient.get<any>('/public/news', {
    params,
  });
}

/**
 * 获取公开新闻详情（用于前台预览）
 */
export async function getPublicNewsDetail(id: number) {
  return requestClient.get<any>(`/public/news/${id}`);
}

/**
 * 获取公开分类列表（用于前台预览）
 */
export async function getPublicCategories() {
  return requestClient.get<any>('/public/news/categories');
}
